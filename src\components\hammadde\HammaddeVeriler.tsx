import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TestTube, Shield, GitBranch, Droplets, Atom, Package2, Cog, 
  TrendingUp, TrendingDown, Minus 
} from 'lucide-react';

// Ham madde listesi
const hammaddeler = [
  { id: 'STAB1', name: 'STAB1', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'STAB2', name: 'STAB2', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'MUKAVEMET', name: 'MUKAVEMET', icon: Shield, color: 'text-red-600', bgColor: 'bg-red-100' },
  { id: 'PROSES', name: 'PROSES', icon: GitBranch, color: 'text-purple-600', bgColor: 'bg-purple-100' },
  { id: 'ENJEKSIYON', name: 'ENJEKSIYON', icon: Droplets, color: 'text-cyan-600', bgColor: 'bg-cyan-100' },
  { id: 'KALSIT', name: 'KALSİT', icon: Atom, color: 'text-orange-600', bgColor: 'bg-orange-100' },
  { id: 'PVC', name: 'PVC', icon: Package2, color: 'text-green-600', bgColor: 'bg-green-100' },
  { id: 'MOT12', name: 'MOT12', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT13', name: 'MOT13', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT14', name: 'MOT14', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT15', name: 'MOT15', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT16', name: 'MOT16', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT17', name: 'MOT17', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT18', name: 'MOT18', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
];

interface HammaddeData {
  id: string;
  anlik: number;
  set: number;
  stok: number;
  birim: string;
}

export const HammaddeVeriler = () => {
  const [hammaddeData, setHammaddeData] = useState<HammaddeData[]>([]);
  const [loading, setLoading] = useState(true);

  // Gerçek API'den veri çek
  useEffect(() => {
    const fetchHammaddeData = async () => {
      try {
        setLoading(true);

        // Paralel olarak tüm ham madde verilerini çek
        const [anlikResponse, setResponse, stokResponse] = await Promise.all([
          fetch('/api/hammadde/anlik'),
          fetch('/api/hammadde/set'),
          fetch('/api/hammadde/stok')
        ]);

        const anlikData = await anlikResponse.json();
        const setData = await setResponse.json();
        const stokData = await stokResponse.json();

        // Verileri birleştir
        const combinedData: HammaddeData[] = hammaddeler.map(h => {
          const anlik = anlikData.find((a: any) => a.hammadde_id === h.id)?.deger || 0;
          const set = setData.find((s: any) => s.hammadde_id === h.id)?.deger || 0;
          const stok = stokData.find((st: any) => st.hammadde_id === h.id)?.miktar || 0;

          return {
            id: h.id,
            anlik,
            set,
            stok,
            birim: 'kg'
          };
        });

        setHammaddeData(combinedData);
      } catch (error) {
        console.error('Ham madde verileri çekilemedi:', error);
        // Hata durumunda mock data kullan
        const mockData: HammaddeData[] = hammaddeler.map(h => ({
          id: h.id,
          anlik: Math.random() * 1000,
          set: Math.random() * 1200,
          stok: Math.random() * 5000,
          birim: 'kg'
        }));
        setHammaddeData(mockData);
      } finally {
        setLoading(false);
      }
    };

    fetchHammaddeData();

    // 30 saniyede bir verileri güncelle
    const interval = setInterval(fetchHammaddeData, 30000);

    return () => clearInterval(interval);
  }, []);

  const getTrendIcon = (anlik: number, set: number) => {
    const diff = anlik - set;
    if (Math.abs(diff) < 10) return <Minus className="h-4 w-4 text-gray-500" />;
    return diff > 0 ? 
      <TrendingUp className="h-4 w-4 text-green-500" /> : 
      <TrendingDown className="h-4 w-4 text-red-500" />;
  };

  const getStokDurumu = (stok: number) => {
    if (stok < 1000) return { label: 'Düşük', variant: 'destructive' as const };
    if (stok < 3000) return { label: 'Orta', variant: 'secondary' as const };
    return { label: 'Yüksek', variant: 'default' as const };
  };

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {Array.from({ length: 14 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {hammaddeler.map((hammadde) => {
          const data = hammaddeData.find(d => d.id === hammadde.id);
          const stokDurumu = data ? getStokDurumu(data.stok) : { label: 'Bilinmiyor', variant: 'secondary' as const };
          
          return (
            <Card key={hammadde.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-1.5 rounded-md ${hammadde.bgColor}`}>
                      <hammadde.icon className={`h-4 w-4 ${hammadde.color}`} />
                    </div>
                    <CardTitle className="text-sm font-medium">{hammadde.name}</CardTitle>
                  </div>
                  {data && getTrendIcon(data.anlik, data.set)}
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {data ? (
                  <>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-muted-foreground">ANLIK:</span>
                        <div className="font-semibold text-emerald-600">
                          {data.anlik.toFixed(1)} {data.birim}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">SET:</span>
                        <div className="font-semibold text-blue-600">
                          {data.set.toFixed(1)} {data.birim}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="text-xs">
                        <span className="text-muted-foreground">STOK:</span>
                        <div className="font-semibold">
                          {data.stok.toFixed(0)} {data.birim}
                        </div>
                      </div>
                      <Badge variant={stokDurumu.variant} className="text-xs">
                        {stokDurumu.label}
                      </Badge>
                    </div>
                  </>
                ) : (
                  <div className="text-xs text-muted-foreground">
                    Veri yükleniyor...
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
