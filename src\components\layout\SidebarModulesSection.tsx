import {
  LucideIcon, BarChart3, Users, Package, ShoppingCart, Factory, Wrench, Truck, Briefcase, Settings,
  TestTube, Shield, GitBranch, Droplets, Atom, Package2, Cog, Layers
} from "lucide-react";
import { NavMenuItem } from "./NavMenuItem";
import { useState } from "react";
import { FinanceSubMenu } from "./FinanceSubMenu";
import { HammaddeSubMenu } from "./HammaddeSubMenu";
import { useAuth } from "@/contexts/AuthContext";

interface ModuleItem {
  name: string;
  path: string;
  icon: LucideIcon;
  moduleKey: string;
}

const modules: ModuleItem[] = [
  {
    name: "<PERSON>or<PERSON>",
    path: "/raporlar",
    icon: BarChart3,
    moduleKey: "all"
  },
  {
    name: "Finans",
    path: "/finans",
    icon: Briefcase,
    moduleKey: "finans"
  },
  {
    name: "<PERSON><PERSON>şteriler",
    path: "/musteriler",
    icon: Users,
    moduleKey: "musteri"
  },
  {
    name: "<PERSON>vant<PERSON>",
    path: "/envanter",
    icon: Package,
    moduleKey: "stok"
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    path: "/siparisler",
    icon: ShoppingCart,
    moduleKey: "satis"
  },
  {
    name: "Üretim",
    path: "/uretim",
    icon: Factory,
    moduleKey: "uretim"
  },
  {
    name: "Bakım",
    path: "/bakim",
    icon: Wrench,
    moduleKey: "bakim"
  },
  {
    name: "Araçlar",
    path: "/araclar",
    icon: Truck,
    moduleKey: "araclar"
  },
  {
    name: "Ekip Yönetimi",
    path: "/ekip",
    icon: Users,
    moduleKey: "insan_kaynaklari"
  },
  {
    name: "Ayarlar",
    path: "/ayarlar",
    icon: Settings,
    moduleKey: "all"
  },
  {
    name: "STAB1",
    path: "/stab1",
    icon: TestTube,
    moduleKey: "stab1"
  },
  {
    name: "STAB2",
    path: "/stab2",
    icon: TestTube,
    moduleKey: "stab2"
  },
  {
    name: "MUKAVEMET",
    path: "/mukavemet",
    icon: Shield,
    moduleKey: "mukavemet"
  },
  {
    name: "PROSES",
    path: "/proses",
    icon: GitBranch,
    moduleKey: "proses"
  },
  {
    name: "ENJEKSIYON",
    path: "/enjeksiyon",
    icon: Droplets,
    moduleKey: "enjeksiyon"
  },
  {
    name: "KALSİT",
    path: "/kalsit",
    icon: Atom,
    moduleKey: "kalsit"
  },
  {
    name: "PVC",
    path: "/pvc",
    icon: Package2,
    moduleKey: "pvc"
  },
  {
    name: "MOT12",
    path: "/mot12",
    icon: Cog,
    moduleKey: "mot12"
  },
  {
    name: "MOT13",
    path: "/mot13",
    icon: Cog,
    moduleKey: "mot13"
  },
  {
    name: "MOT14",
    path: "/mot14",
    icon: Cog,
    moduleKey: "mot14"
  },
  {
    name: "MOT15",
    path: "/mot15",
    icon: Cog,
    moduleKey: "mot15"
  },
  {
    name: "MOT16",
    path: "/mot16",
    icon: Cog,
    moduleKey: "mot16"
  },
  {
    name: "MOT17",
    path: "/mot17",
    icon: Cog,
    moduleKey: "mot17"
  },
  {
    name: "MOT18",
    path: "/mot18",
    icon: Cog,
    moduleKey: "mot18"
  },
  {
    name: "HAMMADDE",
    path: "/hammadde",
    icon: Layers,
    moduleKey: "hammadde"
  },
];

export const SidebarModulesSection = () => {
  // We'll initialize the state based on the current path to keep the menu open when appropriate
  const { company, user } = useAuth();
  const [openFinanceMenu, setOpenFinanceMenu] = useState(() => {
    // Check if we're on a finance page to initially open the menu
    const path = window.location.pathname;
    return path.startsWith('/finans');
  });

  const [openHammaddeMenu, setOpenHammaddeMenu] = useState(() => {
    // Check if we're on a hammadde page to initially open the menu
    const path = window.location.pathname;
    return path.startsWith('/hammadde');
  });

  // Check if the user is the muhasebe user (Senay Yılmaz with ID 5)
  const isMuhasebeUser = user?.id === 5;

  // For muhasebe user, only show finance module
  const filteredModules = isMuhasebeUser
    ? modules.filter(m => m.moduleKey === "finans" || m.name === "Ayarlar")
    : modules.filter(module => {
      if (!company || !user) return false;

      // Admin can see everything
      if (user.role === "Admin") return true;

      // Check if the company has access to this module
      const hasModuleAccess =
        company.modules.includes(module.moduleKey) ||
        company.optionalModules.includes(module.moduleKey) ||
        module.moduleKey === "all";

      // Check if the user has permission for this module
      const userHasPermission =
        user.permissions.includes(module.moduleKey) ||
        user.permissions.includes("all");

      return hasModuleAccess && userHasPermission;
    });

  return (
    <div className="px-3 py-2">
      <div className="mb-2 px-4 text-xs font-semibold text-muted-foreground">
        Modüller
      </div>
      <div className="space-y-1">
        {filteredModules.map((module) => {
          if (module.name === "Finans" && (company?.modules.includes("finans") || user?.permissions.includes("finans"))) {
            return (
              <FinanceSubMenu
                key={module.path}
                openFinanceMenu={openFinanceMenu}
                setOpenFinanceMenu={setOpenFinanceMenu}
                iconColor={isMuhasebeUser ? "text-blue-500" : ""}
              />
            );
          }

          if (module.name === "HAMMADDE" && (company?.modules.includes("hammadde") || user?.permissions.includes("hammadde") || user?.role === "Admin")) {
            return (
              <HammaddeSubMenu
                key={module.path}
                openHammaddeMenu={openHammaddeMenu}
                setOpenHammaddeMenu={setOpenHammaddeMenu}
                iconColor="text-emerald-600"
              />
            );
          }

          return (
            <NavMenuItem
              key={module.path}
              icon={module.icon}
              title={module.name}
              href={module.path}
            />
          );
        })}
      </div>
    </div>
  );
}
