import { LucideIcon, BarChart3, Users, Package, ShoppingCart, Factory, Wrench, Truck, Briefcase, Settings } from "lucide-react";
import { NavMenuItem } from "./NavMenuItem";
import { useState } from "react";
import { FinanceSubMenu } from "./FinanceSubMenu";
import { useAuth } from "@/contexts/AuthContext";

interface ModuleItem {
  name: string;
  path: string;
  icon: LucideIcon;
  moduleKey: string;
}

const modules: ModuleItem[] = [
  {
    name: "Raporlar",
    path: "/raporlar",
    icon: BarChart3,
    moduleKey: "all"
  },
  {
    name: "Finans",
    path: "/finans",
    icon: Briefcase,
    moduleKey: "finans"
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    path: "/musteriler",
    icon: Users,
    moduleKey: "musteri"
  },
  {
    name: "Envanter",
    path: "/envanter",
    icon: Package,
    moduleKey: "stok"
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    path: "/siparisler",
    icon: ShoppingCart,
    moduleKey: "satis"
  },
  {
    name: "Üretim",
    path: "/uretim",
    icon: Factory,
    moduleKey: "uretim"
  },
  {
    name: "Bakı<PERSON>",
    path: "/bakim",
    icon: <PERSON>ch,
    moduleKey: "bakim"
  },
  {
    name: "Araçlar",
    path: "/araclar",
    icon: Truck,
    moduleKey: "araclar"
  },
  {
    name: "Ekip Yönetimi",
    path: "/ekip",
    icon: Users,
    moduleKey: "insan_kaynaklari"
  },
  {
    name: "Ayarlar",
    path: "/ayarlar",
    icon: Settings,
    moduleKey: "all"
  },
];

export const SidebarModulesSection = () => {
  // We'll initialize the state based on the current path to keep the menu open when appropriate
  const { company, user } = useAuth();
  const [openFinanceMenu, setOpenFinanceMenu] = useState(() => {
    // Check if we're on a finance page to initially open the menu
    const path = window.location.pathname;
    return path.startsWith('/finans');
  });

  // Check if the user is the muhasebe user (Senay Yılmaz with ID 5)
  const isMuhasebeUser = user?.id === 5;

  // For muhasebe user, only show finance module
  const filteredModules = isMuhasebeUser
    ? modules.filter(m => m.moduleKey === "finans" || m.name === "Ayarlar")
    : modules.filter(module => {
      if (!company || !user) return false;

      // Admin can see everything
      if (user.role === "Admin") return true;

      // Check if the company has access to this module
      const hasModuleAccess =
        company.modules.includes(module.moduleKey) ||
        company.optionalModules.includes(module.moduleKey) ||
        module.moduleKey === "all";

      // Check if the user has permission for this module
      const userHasPermission =
        user.permissions.includes(module.moduleKey) ||
        user.permissions.includes("all");

      return hasModuleAccess && userHasPermission;
    });

  return (
    <div className="px-3 py-2">
      <div className="mb-2 px-4 text-xs font-semibold text-muted-foreground">
        Modüller
      </div>
      <div className="space-y-1">
        {filteredModules.map((module) => {
          if (module.name === "Finans" && (company?.modules.includes("finans") || user?.permissions.includes("finans"))) {
            return (
              <FinanceSubMenu
                key={module.path}
                openFinanceMenu={openFinanceMenu}
                setOpenFinanceMenu={setOpenFinanceMenu}
                iconColor={isMuhasebeUser ? "text-blue-500" : ""}
              />
            );
          }

          return (
            <NavMenuItem
              key={module.path}
              icon={module.icon}
              title={module.name}
              href={module.path}
            />
          );
        })}
      </div>
    </div>
  );
}
