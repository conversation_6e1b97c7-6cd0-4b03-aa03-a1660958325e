import React from "react";
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

export type ProductionLine = "line1" | "line2" | "line3" | "line4" | "line5" | "borular";

interface ProductionLineTabsProps {
  activeLine: ProductionLine;
  onLineChange: (line: ProductionLine) => void;
}

export const ProductionLineTabs: React.FC<ProductionLineTabsProps> = ({
  activeLine,
  onLineChange,
}) => {
  // Define theme colors for each production line
  const lineThemes = {
    line1: "bg-[#F2FCE2] hover:bg-[#E6F6D1] data-[state=active]:bg-[#D1EFB7] data-[state=active]:text-green-800",
    line2: "bg-[#FEF7CD] hover:bg-[#FDEEAA] data-[state=active]:bg-[#FBE787] data-[state=active]:text-amber-800",
    line3: "bg-[#D3E4FD] hover:bg-[#BDDAFC] data-[state=active]:bg-[#A1CBFC] data-[state=active]:text-blue-800",
    line4: "bg-[#FCE7F3] hover:bg-[#FBCFE8] data-[state=active]:bg-[#F9A8D4] data-[state=active]:text-pink-800",
    line5: "bg-[#F0FDF4] hover:bg-[#DCFCE7] data-[state=active]:bg-[#BBF7D0] data-[state=active]:text-green-800",
    borular: "bg-purple-200 hover:bg-purple-300 data-[state=active]:bg-purple-400 data-[state=active]:text-purple-800",
  };

  return (
    <div className="mb-6">
      <Tabs value={activeLine} onValueChange={(value) => onLineChange(value as ProductionLine)}>
        <TabsList className="w-full bg-white border rounded-lg p-1 grid grid-cols-6 gap-1">
          <TabsTrigger
            value="line1"
            className={cn("rounded-md py-3 font-medium text-sm", lineThemes.line1)}
          >
            Üretim Hattı 1
          </TabsTrigger>
          <TabsTrigger
            value="line2"
            className={cn("rounded-md py-3 font-medium text-sm", lineThemes.line2)}
          >
            Üretim Hattı 2
          </TabsTrigger>
          <TabsTrigger
            value="line3"
            className={cn("rounded-md py-3 font-medium text-sm", lineThemes.line3)}
          >
            Üretim Hattı 3
          </TabsTrigger>
          <TabsTrigger
            value="line4"
            className={cn("rounded-md py-3 font-medium text-sm", lineThemes.line4)}
          >
            Üretim Hattı 4
          </TabsTrigger>
          <TabsTrigger
            value="line5"
            className={cn("rounded-md py-3 font-medium text-sm", lineThemes.line5)}
          >
            Üretim Hattı 5
          </TabsTrigger>
          <TabsTrigger
            value="borular"
            className={cn("rounded-md py-3 font-medium text-sm", lineThemes.borular)}
          >
            Borular
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};
