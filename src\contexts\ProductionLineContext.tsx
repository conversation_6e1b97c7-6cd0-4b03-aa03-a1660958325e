import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';

export type ProductionLine = "line1" | "line2" | "line3" | "line4" | "line5" | "borular";

interface ProductionLineContextType {
  activeLine: ProductionLine;
  setActiveLine: (line: ProductionLine) => void;
  getLineThemeColor: () => string;
  getLineAccentColor: () => string;
}

const defaultContext: ProductionLineContextType = {
  activeLine: "line1",
  setActiveLine: () => { },
  getLineThemeColor: () => "#F2FCE2",
  getLineAccentColor: () => "#D1EFB7",
};

const ProductionLineContext = createContext<ProductionLineContextType>(defaultContext);

export const useProductionLine = () => useContext(ProductionLineContext);

interface ProductionLineProviderProps {
  children: ReactNode;
  initialLine?: ProductionLine;
}

export const ProductionLineProvider: React.FC<ProductionLineProviderProps> = ({
  children,
  initialLine = "line1"
}) => {
  const [activeLine, setActiveLine] = useState<ProductionLine>(initialLine);

  useEffect(() => {
    console.log("ProductionLineContext - Aktif hat değişti:", activeLine);
  }, [activeLine]);

  const handleLineChange = (line: ProductionLine) => {
    console.log("ProductionLineContext - Hat değişiyor:", line);
    setActiveLine(line);
  };

  const getLineThemeColor = () => {
    switch (activeLine) {
      case "line1":
        return "#F2FCE2"; // Light green
      case "line2":
        return "#FEF7CD"; // Light yellow
      case "line3":
        return "#D3E4FD"; // Light blue
      case "line4":
        return "#FCE7F3"; // Light pink
      case "line5":
        return "#F0FDF4"; // Light mint green
      case "borular":
        return "#EDE9FE"; // Light purple for Borular
      default:
        return "#F2FCE2";
    }
  };

  const getLineAccentColor = () => {
    switch (activeLine) {
      case "line1":
        return "#D1EFB7"; // Medium green
      case "line2":
        return "#FBE787"; // Medium yellow
      case "line3":
        return "#A1CBFC"; // Medium blue
      case "line4":
        return "#F9A8D4"; // Medium pink
      case "line5":
        return "#BBF7D0"; // Medium mint green
      case "borular":
        return "#C4B5FD"; // Medium purple for Borular
      default:
        return "#D1EFB7";
    }
  };

  return (
    <ProductionLineContext.Provider
      value={{
        activeLine,
        setActiveLine: handleLineChange,
        getLineThemeColor,
        getLineAccentColor
      }}
    >
      {children}
    </ProductionLineContext.Provider>
  );
};
