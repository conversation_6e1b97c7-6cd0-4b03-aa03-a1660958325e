import React from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { TabsNavigation, TabItem } from '@/components/layout/TabsNavigation';
import { HammaddeVeriler } from '@/components/hammadde/HammaddeVeriler';
import { HammaddeStokGirisi } from '@/components/hammadde/HammaddeStokGirisi';
import { Layers } from 'lucide-react';

const Hammadde = () => {
  const tabs: TabItem[] = [
    { 
      id: "veriler", 
      label: "Veriler", 
      component: <HammaddeVeriler /> 
    },
    { 
      id: "stok-girisi", 
      label: "Stok Girişi", 
      component: <HammaddeStokGirisi /> 
    }
  ];

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-emerald-100 rounded-lg">
            <Layers className="h-6 w-6 text-emerald-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">HAMMADDE</h1>
            <p className="text-muted-foreground">
              Ham madde yönetimi ve stok kontrol sistemi
            </p>
          </div>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default Hammadde;
