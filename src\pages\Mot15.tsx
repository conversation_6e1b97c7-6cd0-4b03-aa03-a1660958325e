import React from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Cog } from 'lucide-react';

const Mot15 = () => {
  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-slate-100 rounded-lg">
            <Cog className="h-6 w-6 text-slate-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">MOT15</h1>
            <p className="text-muted-foreground">
              MOT15 motor kontrol modülü
            </p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Genel Bilgiler</CardTitle>
              <CardDescription>
                MOT15 modülü temel bilgileri
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Bu modül henüz yapılandırılmamıştır. Lütfen sistem yöneticisi ile iletişime geçin.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Durum</CardTitle>
              <CardDescription>
                Sistem durumu
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Aktif</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>İşlemler</CardTitle>
              <CardDescription>
                Mevcut işlemler
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Henüz tanımlanmış işlem bulunmamaktadır.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Mot15;
