import { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import UretimVerileri from "@/components/production/uretim-verileri/UretimVerileri";
import { ProductionLineTabs, type ProductionLine } from "@/components/production/ProductionLineTabs";
import { ProductionLineProvider } from "@/contexts/ProductionLineContext";
import UrunDonusum from "@/components/production/uretim-verileri/UrunDonusum";

const Uretim = () => {
  const [activeLine, setActiveLine] = useState<ProductionLine>("line1");

  // Hat değişikliğini izle
  useEffect(() => {
    console.log("Uretim sayfası - Aktif hat değişti:", activeLine);
  }, [activeLine]);

  // Hat değişikliği için geliştirilmiş fonksiyon
  const handleLineChange = (line: ProductionLine) => {
    console.log("Hat değişimi - Önceki:", activeLine, "Yeni:", line);
    setActiveLine(line);
  };

  // Generate theme class based on active production line
  const getLineThemeClass = () => {
    switch (activeLine) {
      case "line1":
        return "bg-gradient-to-r from-[#F2FCE2]/50 to-white";
      case "line2":
        return "bg-gradient-to-r from-[#FEF7CD]/50 to-white";
      case "line3":
        return "bg-gradient-to-r from-[#D3E4FD]/50 to-white";
      case "line4":
        return "bg-gradient-to-r from-[#FCE7F3]/50 to-white";
      case "line5":
        return "bg-gradient-to-r from-[#F0FDF4]/50 to-white";
      case "borular":
        return "bg-gradient-to-r from-[#EDE9FE]/50 to-white";
      default:
        return "";
    }
  };

  return (
    <MainLayout>
      <ProductionLineProvider initialLine={activeLine} key={activeLine}>
        <div className={`relative p-4 ${getLineThemeClass()} min-h-screen transition-colors`}>
          <ProductionLineTabs activeLine={activeLine} onLineChange={handleLineChange} />
          {activeLine === "borular" ? (
            <UrunDonusum />
          ) : (
            <UretimVerileri currentLine={activeLine} />
          )}
          <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-full shadow-lg flex items-center space-x-2 text-xs animate-pulse z-50">
            <span className="bg-white rounded-full h-2 w-2 animate-ping"></span>
            <span>Veri otomatik güncelleniyor</span>
          </div>
        </div>
      </ProductionLineProvider>
    </MainLayout>
  );
};

export default Uretim;
